package io.securinest.controlplaneapi.entity.policy;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import io.securinest.controlplaneapi.entity.shared.EnforcementMode;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "policy_bundle")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class PolicyBundle extends AbstractTenantEntity {

    @NotNull
    @Column(name = "policy_version_id", nullable = false)
    private UUID policyVersionId;

    @NotNull
    @Column(name = "service_id", nullable = false)
    private UUID serviceId;

    @NotNull
    @Column(name = "env_id", nullable = false)
    private UUID envId;

    @NotNull
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "compiled_json", nullable = false, columnDefinition = "jsonb")
    private Map<String, Object> compiledJson;

    @NotBlank
    @Size(max = 64)
    @Column(name = "etag", nullable = false, length = 64)
    private String etag;

    @NotNull
    @Column(name = "signature_b64", nullable = false)
    private String signatureB64;

    @NotBlank
    @Size(max = 64)
    @Column(name = "kid", nullable = false, length = 64)
    private String kid;

    @Column(name = "active", nullable = false)
    private boolean active = true;

    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "enforcement_mode", nullable = false)
    private EnforcementMode enforcementMode = EnforcementMode.SHADOW;

    @Column(name = "lkg_ttl_seconds")
    private Integer lkgTtlSeconds;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "sampling", columnDefinition = "jsonb")
    private String sampling;

}
