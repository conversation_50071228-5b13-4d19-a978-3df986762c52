package io.securinest.controlplaneapi.exceptionhandling;

import java.io.Serial;

public class SecurinestConfigurationException extends IllegalStateException {
    @Serial
    private static final long serialVersionUID = 1L;

    public SecurinestConfigurationException() {
        super();
    }

    public SecurinestConfigurationException(String message) {
        super(message);
    }

    public SecurinestConfigurationException(String message, Throwable cause) {
        super(message, cause);
    }

    public SecurinestConfigurationException(Throwable cause) {
        super(cause);
    }

    public static SecurinestConfigurationException forKey(String key, String detail) {
        String msg = (detail == null || detail.isBlank())
                ? "Misconfiguration: " + key
                : "Misconfiguration: " + key + " — " + detail;
        return new SecurinestConfigurationException(msg);
    }
}
