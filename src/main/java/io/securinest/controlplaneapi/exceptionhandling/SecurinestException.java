package io.securinest.controlplaneapi.exceptionhandling;

import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

public class SecurinestException extends ResponseStatusException {

    public SecurinestException(String message) {
        super(HttpStatus.INTERNAL_SERVER_ERROR, message);
    }

    public SecurinestException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
    }

}
