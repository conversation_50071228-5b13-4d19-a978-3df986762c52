package io.securinest.controlplaneapi.mapper.audit;

import io.securinest.controlplaneapi.dto.audit.AuditLogEntryResponse;
import io.securinest.controlplaneapi.dto.audit.AuditPackResponse;
import io.securinest.controlplaneapi.dto.audit.AuditPackScopeResponse;
import io.securinest.controlplaneapi.entity.audit.AuditLogEntry;
import io.securinest.controlplaneapi.entity.audit.AuditPack;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;
import java.util.Map;

@Mapper(
        componentModel = "spring",
        injectionStrategy = InjectionStrategy.CONSTRUCTOR,
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface AuditMapper {

    @Mapping(target = "id", expression = "java(entity.getId() != null ? entity.getId().toString() : null)")
    @Mapping(target = "tenantId", expression = "java(entity.getTenantId() != null ? entity.getTenantId().toString() : null)")
    @Mapping(target = "actorUserId", expression = "java(entity.getActorUserId() != null ? entity.getActorUserId().toString() : null)")
    @Mapping(target = "action", expression = "java(entity.getAction() != null ? entity.getAction().name() : null)")
    @Mapping(target = "targetId", expression = "java(entity.getTargetId() != null ? entity.getTargetId().toString() : null)")
    AuditLogEntryResponse toResponse(AuditLogEntry entity);

    @Mapping(target = "id", expression = "java(entity.getId() != null ? entity.getId().toString() : null)")
    @Mapping(target = "tenantId", expression = "java(entity.getTenantId() != null ? entity.getTenantId().toString() : null)")
    @Mapping(target = "status", expression = "java(entity.getStatus() != null ? entity.getStatus().name() : null)")
    @Mapping(target = "createdBy", expression = "java(entity.getCreatedBy() != null ? entity.getCreatedBy().toString() : null)")
    @Mapping(target = "scope", expression = "java(mapScope(entity.getScope()))")
    AuditPackResponse toResponse(AuditPack entity);

    /**
     * Maps the scope JSON to AuditPackScopeResponse
     */
    default AuditPackScopeResponse mapScope(Map<String, Object> scope) {
        if (scope == null) {
            return null;
        }

        @SuppressWarnings("unchecked")
        List<String> services = (List<String>) scope.get("services");
        
        @SuppressWarnings("unchecked")
        List<String> envs = (List<String>) scope.get("envs");
        
        @SuppressWarnings("unchecked")
        Map<String, Object> dateRangeMap = (Map<String, Object>) scope.get("dateRange");
        
        io.securinest.controlplaneapi.dto.shared.TimeRangeResponse dateRange = null;
        if (dateRangeMap != null) {
            String from = (String) dateRangeMap.get("from");
            String to = (String) dateRangeMap.get("to");
            dateRange = io.securinest.controlplaneapi.dto.shared.TimeRangeResponse.builder()
                    .from(from != null ? java.time.Instant.parse(from) : null)
                    .to(to != null ? java.time.Instant.parse(to) : null)
                    .build();
        }

        return AuditPackScopeResponse.builder()
                .services(services)
                .envs(envs)
                .dateRange(dateRange)
                .build();
    }
}
