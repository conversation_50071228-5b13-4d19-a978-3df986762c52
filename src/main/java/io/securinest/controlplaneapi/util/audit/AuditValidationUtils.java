package io.securinest.controlplaneapi.util.audit;

import io.securinest.controlplaneapi.entity.shared.AuditAction;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;

import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@UtilityClass
public class AuditValidationUtils {

    // Maximum number of actions/target types in a single query
    private static final int MAX_FILTER_ITEMS = 20;
    
    // Maximum date range for audit queries (180 days)
    private static final Duration MAX_DATE_RANGE = Duration.ofDays(180);
    
    // Default date range if none provided (30 days)
    private static final Duration DEFAULT_DATE_RANGE = Duration.ofDays(30);
    
    // Maximum date range for audit pack creation (180 days)
    private static final Duration MAX_AUDIT_PACK_DATE_RANGE = Duration.ofDays(180);

    // Allowed target types for audit log queries
    private static final Set<String> ALLOWED_TARGET_TYPES = Set.of(
        "TENANT",
        "ENVIRONMENT", 
        "SERVICE",
        "POLICY",
        "POLICY_VERSION",
        "BUNDLE",
        "API_KEY",
        "SCM_ACCOUNT",
        "SCM_REPO",
        "AUDIT_PACK",
        "USER_ACCOUNT",
        "TENANT_MEMBER",
        "TENANT_INVITE",
        "TENANT_DOMAIN",
        "SIGNING_KEY",
        "COMPLIANCE_FINDING",
        "SCM_SCAN_JOB",
        "SCM_SCAN_FINDING",
        "POLICY_SUGGESTION",
        "WEBHOOK_ENDPOINT"
    );

    // Allowed sort fields for audit logs
    private static final Set<String> ALLOWED_AUDIT_LOG_SORT_FIELDS = Set.of("ts");
    
    // Allowed sort fields for audit packs
    private static final Set<String> ALLOWED_AUDIT_PACK_SORT_FIELDS = Set.of("createdAt");

    /**
     * Validates and normalizes action filter
     */
    public static List<AuditAction> validateAndNormalizeActions(String actionsParam) {
        if (actionsParam == null || actionsParam.trim().isEmpty()) {
            return null;
        }

        List<String> actionStrings = Arrays.stream(actionsParam.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();

        if (actionStrings.size() > MAX_FILTER_ITEMS) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Too many actions specified. Maximum allowed: " + MAX_FILTER_ITEMS);
        }

        return actionStrings.stream()
                .map(actionString -> {
                    try {
                        return AuditAction.valueOf(actionString.toUpperCase());
                    } catch (IllegalArgumentException e) {
                        throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                            "Unknown action: " + actionString);
                    }
                })
                .collect(Collectors.toList());
    }

    /**
     * Validates and normalizes target type filter
     */
    public static List<String> validateAndNormalizeTargetTypes(String targetTypesParam) {
        if (targetTypesParam == null || targetTypesParam.trim().isEmpty()) {
            return null;
        }

        List<String> targetTypes = Arrays.stream(targetTypesParam.split(","))
                .map(String::trim)
                .map(String::toUpperCase)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());

        if (targetTypes.size() > MAX_FILTER_ITEMS) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Too many target types specified. Maximum allowed: " + MAX_FILTER_ITEMS);
        }

        for (String targetType : targetTypes) {
            if (!ALLOWED_TARGET_TYPES.contains(targetType)) {
                throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                    "Unknown target type: " + targetType);
            }
        }

        return targetTypes;
    }

    /**
     * Validates UUID parameter
     */
    public static UUID validateUuid(String uuidParam, String fieldName) {
        if (uuidParam == null || uuidParam.trim().isEmpty()) {
            return null;
        }

        try {
            return UUID.fromString(uuidParam.trim());
        } catch (IllegalArgumentException e) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Invalid " + fieldName + " format");
        }
    }

    /**
     * Validates and normalizes date range for audit log queries
     */
    public static DateRange validateAndNormalizeDateRange(String fromParam, String toParam) {
        Instant now = Instant.now();
        Instant from;
        Instant to;

        if (fromParam == null && toParam == null) {
            // Default to last 30 days
            from = now.minus(DEFAULT_DATE_RANGE);
            to = now;
        } else {
            from = parseInstant(fromParam, "from");
            to = parseInstant(toParam, "to");

            if (from == null || to == null) {
                throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                    "Both 'from' and 'to' must be provided if either is specified");
            }

            if (from.isAfter(to)) {
                throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                    "'from' date must be before or equal to 'to' date");
            }

            Duration range = Duration.between(from, to);
            if (range.compareTo(MAX_DATE_RANGE) > 0) {
                throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                    "Date range too wide. Maximum allowed: " + MAX_DATE_RANGE.toDays() + " days");
            }
        }

        return new DateRange(from, to);
    }

    /**
     * Validates date range for audit pack creation
     */
    public static void validateAuditPackDateRange(Instant from, Instant to) {
        if (from == null || to == null) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Both 'from' and 'to' dates are required for audit pack creation");
        }

        if (from.isAfter(to)) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "'from' date must be before or equal to 'to' date");
        }

        Duration range = Duration.between(from, to);
        if (range.compareTo(MAX_AUDIT_PACK_DATE_RANGE) > 0) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Date range too wide for audit pack. Maximum allowed: " + MAX_AUDIT_PACK_DATE_RANGE.toDays() + " days");
        }
    }

    /**
     * Validates sort parameter for audit logs
     */
    public static String validateAuditLogSort(String sortParam) {
        return validateSort(sortParam, ALLOWED_AUDIT_LOG_SORT_FIELDS, "ts,DESC");
    }

    /**
     * Validates sort parameter for audit packs
     */
    public static String validateAuditPackSort(String sortParam) {
        return validateSort(sortParam, ALLOWED_AUDIT_PACK_SORT_FIELDS, "createdAt,DESC");
    }

    /**
     * Validates pagination parameters
     */
    public static PaginationParams validatePagination(Integer page, Integer size) {
        int safePage = Math.max(page != null ? page : 0, 0);
        int safeSize = Math.min(Math.max(size != null ? size : 20, 1), 200);
        return new PaginationParams(safePage, safeSize);
    }

    private static String validateSort(String sortParam, Set<String> allowedFields, String defaultSort) {
        if (sortParam == null || sortParam.trim().isEmpty()) {
            return defaultSort;
        }

        String[] parts = sortParam.trim().split(",");
        if (parts.length != 2) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Sort parameter must be in format 'field,direction'");
        }

        String field = parts[0].trim();
        String direction = parts[1].trim().toUpperCase();

        if (!allowedFields.contains(field)) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Invalid sort field: " + field + ". Allowed: " + String.join(", ", allowedFields));
        }

        if (!direction.equals("ASC") && !direction.equals("DESC")) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Sort direction must be ASC or DESC");
        }

        return field + "," + direction;
    }

    private static Instant parseInstant(String instantParam, String fieldName) {
        if (instantParam == null || instantParam.trim().isEmpty()) {
            return null;
        }

        try {
            return Instant.parse(instantParam.trim());
        } catch (Exception e) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST, 
                "Invalid " + fieldName + " date format. Expected ISO-8601 instant");
        }
    }

    public record DateRange(Instant from, Instant to) {}
    
    public record PaginationParams(int page, int size) {}
}
