package io.securinest.controlplaneapi.service;

import io.securinest.controlplaneapi.dto.identity.UserAccountCreateRequest;
import io.securinest.controlplaneapi.dto.identity.UserAccountResponse;
import io.securinest.controlplaneapi.entity.identity.UserAccount;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.mapper.identity.UserAccountMapper;
import io.securinest.controlplaneapi.projection.identity.UserPatch;
import io.securinest.controlplaneapi.repository.identity.UserAccountRepository;
import io.securinest.controlplaneapi.util.identity.IdentityUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import java.util.Optional;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.OptimisticLockingFailureException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserService {

    private final UserAccountRepository userAccountRepository;
    private final UserAccountMapper userAccountMapper;

    @Transactional
    public UserAccountResponse signUp(UserAccountCreateRequest req, String requestId) {
        if (req == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
        
        String normalizedEmail = ValidationUtils.normalizeEmail(req.email());
        String normalizedDisplayName = ValidationUtils.trimToNull(req.displayName());

        ValidationUtils.validateEmail(normalizedEmail);
        
        if (normalizedDisplayName != null && normalizedDisplayName.length() > 200) 
            throw new SecurinestException(HttpStatus.BAD_REQUEST, "Display name must not exceed 200 characters");

        Optional<UserAccount> existingUser = userAccountRepository.findByEmailIgnoreCase(normalizedEmail);

        if (existingUser.isPresent()) {
            UserAccount user = existingUser.get();
            if (user.getKcSub().startsWith("dev:")) {
                log.info("signUp idempotent return for dev user. email={} requestId={}", normalizedEmail, requestId);
                return userAccountMapper.toResponse(user);
            } else {
                throw new SecurinestException(HttpStatus.CONFLICT, "USER_EXISTS_VIA_SSO");
            }
        }

        UserAccount newUser = IdentityUtils.createUser(normalizedEmail, normalizedDisplayName);

        try {
            UserAccount savedUser = userAccountRepository.save(newUser);
            savedUser.setKcSub("dev:" + savedUser.getId());
            savedUser = userAccountRepository.save(savedUser);

            log.info("User created via signup. userId={} email={} requestId={}", savedUser.getId(), normalizedEmail, requestId);
            return userAccountMapper.toResponse(savedUser);
        } catch (DataIntegrityViolationException e) {
            log.warn("signUp duplicate email race. email={} requestId={} msg={}", normalizedEmail, requestId, e.getMessage());

            Optional<UserAccount> racedUser = userAccountRepository.findByEmailIgnoreCase(normalizedEmail);
            if (racedUser.isPresent()) {
                UserAccount user = racedUser.get();
                if (user.getKcSub().startsWith("dev:")) {
                    return userAccountMapper.toResponse(user);
                } else {
                    throw new SecurinestException(HttpStatus.CONFLICT, "USER_EXISTS_VIA_SSO");
                }
            }
            throw new SecurinestException(HttpStatus.CONFLICT, "Email already taken");
        }
    }

    public UserAccountResponse getMe(UUID userId) {
        if (userId == null) throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
        
        UserAccount user = userAccountRepository.findById(userId)
                .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "User not found"));

        return userAccountMapper.toResponse(user);
    }

    @Transactional
    public UserAccountResponse updateMe(UUID userId, UserPatch patch, Long ifMatchVersion, String requestId) {
        if (userId == null) throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
        if (patch == null) throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");

        UserAccount user = userAccountRepository.findById(userId)
                .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "User not found"));

        if (ifMatchVersion != null && user.getVersion() != ifMatchVersion)
            throw new SecurinestException(HttpStatus.CONFLICT, "VERSION_MISMATCH");
        
        String normalizedDisplayName = ValidationUtils.trimToNull(patch.displayName());
        
        if (normalizedDisplayName != null && normalizedDisplayName.length() > 200)
            throw new SecurinestException(HttpStatus.BAD_REQUEST, "Display name must not exceed 200 characters");
        
        if (normalizedDisplayName != null) {
            user.setDisplayName(normalizedDisplayName);
        }

        try {
            UserAccount savedUser = userAccountRepository.save(user);
            log.info("User updated. userId={} requestId={}", userId, requestId);
            return userAccountMapper.toResponse(savedUser);
        } catch (OptimisticLockingFailureException e) {
            throw new SecurinestException(HttpStatus.CONFLICT, "VERSION_MISMATCH");
        }
    }
}
