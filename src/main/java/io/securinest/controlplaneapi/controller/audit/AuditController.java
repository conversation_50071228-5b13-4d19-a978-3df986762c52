package io.securinest.controlplaneapi.controller.audit;

import io.securinest.controlplaneapi.dto.audit.AuditLogEntryResponse;
import io.securinest.controlplaneapi.dto.audit.AuditPackRequest;
import io.securinest.controlplaneapi.dto.audit.AuditPackResponse;
import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.service.AuditService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import jakarta.validation.Valid;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/tenants/{tenantId}/audit")
public class AuditController {

    private final AuditService auditService;

    /**
     * GET /v1/tenants/{tenantId}/audit/logs
     * Return tenant-scoped audit trail entries with filtering and pagination
     */
    @GetMapping("/logs")
    public ResponseEntity<PageResponse<AuditLogEntryResponse>> getAuditLogs(
            @PathVariable UUID tenantId,
            @RequestParam(required = false) String action,
            @RequestParam(required = false) String targetType,
            @RequestParam(required = false) String targetId,
            @RequestParam(required = false) String actorUserId,
            @RequestParam(required = false) String from,
            @RequestParam(required = false) String to,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String sort,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        PageResponse<AuditLogEntryResponse> response = auditService.getAuditLogs(
                tenantId,
                debugUserId,
                action,
                targetType,
                targetId,
                actorUserId,
                from,
                to,
                page,
                size,
                sort
        );

        return ResponseEntityUtils.ok(response);
    }

    /**
     * POST /v1/tenants/{tenantId}/audit/packs
     * Create (queue) a build for an audit pack for a requested scope
     */
    @PostMapping("/packs")
    public ResponseEntity<AuditPackResponse> createAuditPack(
            @PathVariable UUID tenantId,
            @Valid @RequestBody AuditPackRequest request,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
            @RequestHeader(name = "X-Request-Id", required = false) String requestId,
            @RequestHeader(name = "Idempotency-Key", required = false) String idempotencyKey) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        AuditPackResponse response = auditService.createAuditPack(
                tenantId,
                debugUserId,
                request,
                idempotencyKey,
                requestId
        );

        return ResponseEntityUtils.created(response);
    }

    /**
     * GET /v1/tenants/{tenantId}/audit/packs
     * List audit packs for a tenant, newest first
     */
    @GetMapping("/packs")
    public ResponseEntity<PageResponse<AuditPackResponse>> listAuditPacks(
            @PathVariable UUID tenantId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) String from,
            @RequestParam(required = false) String to,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String sort,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        PageResponse<AuditPackResponse> response = auditService.listAuditPacks(
                tenantId,
                debugUserId,
                status,
                from,
                to,
                page,
                size,
                sort
        );

        return ResponseEntityUtils.ok(response);
    }

    /**
     * GET /v1/tenants/{tenantId}/audit/packs/{packId}
     * Fetch one pack's metadata (status, hashes, artifact pointer)
     */
    @GetMapping("/packs/{packId}")
    public ResponseEntity<AuditPackResponse> getAuditPack(
            @PathVariable UUID tenantId,
            @PathVariable UUID packId,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        AuditPackResponse response = auditService.getAuditPack(tenantId, packId, debugUserId);

        return ResponseEntityUtils.ok(response);
    }
}
