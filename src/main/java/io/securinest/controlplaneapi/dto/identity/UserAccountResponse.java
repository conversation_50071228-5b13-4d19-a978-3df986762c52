package io.securinest.controlplaneapi.dto.identity;

import io.securinest.controlplaneapi.dto.shared.VersionedResource;
import java.time.Instant;
import lombok.Builder;

@Builder
public record UserAccountResponse(
        String id,
        String kcSub,
        String email,
        String displayName,
        Instant createdAt,
        Instant updatedAt,
        Long version
) implements VersionedResource {
}
