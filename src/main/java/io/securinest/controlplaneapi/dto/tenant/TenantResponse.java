package io.securinest.controlplaneapi.dto.tenant;

import io.securinest.controlplaneapi.dto.shared.VersionedResource;
import java.time.Instant;
import lombok.Builder;

@Builder
public record TenantResponse(
        String id,
        String name,
        String slug,
        String billingPlan,
        String region,
        Instant createdAt,
        Instant updatedAt,
        Long version
) implements VersionedResource {
}
